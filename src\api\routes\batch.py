from fastapi import APIRouter, HTTPException, Depends, status
from typing import List, Dict, Any, Optional
import logging
import uuid
from datetime import datetime

from src.models.schemas import BatchAnalysisRequest, BatchAnalysisResponse, TaskType
from src.services.document_service import DocumentService, get_document_service
from src.services.analysis_service import AnalysisService, get_analysis_service

router = APIRouter()
logger = logging.getLogger(__name__)

# 存储批处理作业状态
batch_jobs = {}


@router.post("/analyze", response_model=BatchAnalysisResponse)
async def batch_analyze(
    request: BatchAnalysisRequest,
    document_service: DocumentService = Depends(get_document_service),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """批量分析文档"""
    try:
        # 验证所有文档是否存在
        for doc_id in request.document_ids:
            doc = await document_service.get_document(doc_id)
            if not doc:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"文档 {doc_id} 不存在"
                )
        
        # 创建批处理作业
        job_id = str(uuid.uuid4())
        batch_jobs[job_id] = {
            "status": "pending",
            "total": len(request.document_ids),
            "completed": 0,
            "document_ids": request.document_ids,
            "tasks": [task.value for task in request.tasks],
            "created_at": datetime.now(),
            "results": {}
        }
        
        # 返回作业信息
        response = BatchAnalysisResponse(
            job_id=job_id,
            status="pending",
            total=len(request.document_ids),
            completed=0,
            created_at=batch_jobs[job_id]["created_at"]
        )
        
        logger.info(f"创建批处理作业: {job_id}，共 {len(request.document_ids)} 个文档，{len(request.tasks)} 个任务")
        
        # 异步处理批量任务（实际项目应使用后台任务队列如Celery）
        # 这里只是简单实现，不推荐在生产环境使用
        # 在后台启动批处理任务
        asyncio.create_task(process_batch_job(job_id, request, document_service, analysis_service))
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建批处理作业失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建批处理作业失败: {str(e)}"
        )


@router.get("/status/{job_id}", response_model=BatchAnalysisResponse)
async def get_batch_status(job_id: str):
    """获取批处理作业状态"""
    try:
        if job_id not in batch_jobs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"批处理作业 {job_id} 不存在"
            )
        
        job = batch_jobs[job_id]
        
        response = BatchAnalysisResponse(
            job_id=job_id,
            status=job["status"],
            total=job["total"],
            completed=job["completed"],
            created_at=job["created_at"]
        )
        
        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取批处理作业状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取批处理作业状态失败: {str(e)}"
        )


# 注意：此函数在实际项目中应改用后台任务队列如Celery实现
async def process_batch_job(
    job_id: str,
    request: BatchAnalysisRequest,
    document_service: DocumentService,
    analysis_service: AnalysisService
):
    """处理批处理作业"""
    try:
        logger.info(f"开始处理批处理作业: {job_id}")
        batch_jobs[job_id]["status"] = "processing"

        for doc_id in request.document_ids:
            try:
                # 获取文档
                document = await document_service.get_document(doc_id)
                if not document:
                    logger.warning(f"文档 {doc_id} 不存在，跳过")
                    batch_jobs[job_id]["results"][doc_id] = {
                        "status": "error",
                        "error": "文档不存在"
                    }
                    continue

                # 执行分析任务
                results = {}
                for task in request.tasks:
                    try:
                        # 这里需要调用实际的分析服务
                        # 暂时返回模拟结果
                        results[task.value] = {
                            "status": "completed",
                            "result": f"模拟分析结果 - 任务: {task.value}, 文档: {document.title}"
                        }
                    except Exception as task_error:
                        logger.error(f"任务 {task.value} 执行失败: {task_error}")
                        results[task.value] = {
                            "status": "error",
                            "error": str(task_error)
                        }

                # 保存结果到批处理状态
                batch_jobs[job_id]["results"][doc_id] = {
                    "status": "completed",
                    "document_title": document.title,
                    "results": results
                }

                # 更新作业状态
                batch_jobs[job_id]["completed"] += 1
                logger.info(f"文档 {doc_id} 处理完成 ({batch_jobs[job_id]['completed']}/{batch_jobs[job_id]['total']})")

            except Exception as e:
                logger.error(f"处理文档 {doc_id} 失败: {str(e)}")
                batch_jobs[job_id]["results"][doc_id] = {
                    "status": "error",
                    "document_title": f"文档 {doc_id}",
                    "error": str(e)
                }

        # 更新作业状态
        batch_jobs[job_id]["status"] = "completed"
        logger.info(f"批处理作业 {job_id} 已完成")

    except Exception as e:
        logger.error(f"处理批处理作业 {job_id} 失败: {str(e)}")
        batch_jobs[job_id]["status"] = "failed"
        batch_jobs[job_id]["error"] = str(e)
