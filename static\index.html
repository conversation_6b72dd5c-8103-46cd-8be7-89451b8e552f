<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="titles.systemName">文档分析系统 - 智能政策文档分析平台</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="/static/js/i18n.js"></script>
    <meta name="description" content="基于智谱AI的智能文档分析系统，提供政策文档深度分析、行为者关系识别、因果机制分析等功能">
    <meta name="keywords" content="文档分析, 政策分析, AI分析, 智谱AI, 文本挖掘">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            const icon = document.querySelector('.theme-toggle i');
            if (newTheme === 'dark') {
                icon.textContent = '☀️';
            } else {
                icon.textContent = '🌙';
            }
        }
        
        // 初始化主题
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const icon = document.querySelector('.theme-toggle i');
            if (icon && savedTheme === 'dark') {
                icon.textContent = '☀️';
            }

            // 默认设置为英文
            switchLanguage('en');
        });
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1 data-i18n="titles.systemName">📄 智能文档分析系统</h1>
            <p class="subtitle" data-i18n="titles.subtitle">基于智谱AI的政策文档深度分析工具</p>
            <nav class="main-nav">
                <a href="/" class="nav-link active" data-i18n="nav.home">🏠 文档分析</a>
                <a href="/static/visualization.html" class="nav-link" data-i18n="nav.visualization">📊 数据可视化</a>
                <a href="/static/data-table.html" class="nav-link" data-i18n="nav.tableView">📋 表格视图</a>
                <a href="/static/prompts.html" class="nav-link" data-i18n="nav.promptEditor">🔧 提示词编辑器</a>
                <a href="/docs" class="nav-link" data-i18n="nav.apiDocs">📚 API文档</a>
                <div class="language-selector">
                    <button data-lang="zh" onclick="switchLanguage('zh')">中文</button>
                    <button data-lang="en" onclick="switchLanguage('en')" class="active">English</button>
                </div>
                <button class="theme-toggle" onclick="toggleTheme()" data-i18n="analysis.switchTheme" title="Switch Theme">
                    <i>🌙</i>
                </button>
            </nav>
        </header>

        <main>
            <!-- API配置区域 -->
            <section class="config-section">
                <h2 data-i18n="analysis.apiConfig">⚙️ API配置</h2>
                <div class="form-group">
                    <label for="api-key" data-i18n="analysis.apiKey">智谱AI API密钥</label>
                    <input type="password" id="api-key" data-i18n="analysis.apiKeyPlaceholder" placeholder="请输入您的智谱AI API密钥（可选，服务器端已配置则无需填写）">
                    <small class="hint"><span data-i18n="analysis.apiKeyHint">如需获取API密钥，请访问</span> <a href="https://open.bigmodel.cn/" target="_blank" data-i18n="analysis.apiKeyLink">智谱AI开放平台</a></small>
                </div>
                <div class="form-group">
                    <label for="base-url" data-i18n="analysis.baseUrl">API Base URL</label>
                    <input type="text" id="base-url" data-i18n="analysis.baseUrlPlaceholder" placeholder="默认: https://open.bigmodel.cn/api/paas/v4">
                    <small class="hint" data-i18n="analysis.baseUrlHint">如果您需要使用自定义的智谱AI端点，请在此输入</small>
                </div>
            </section>

            <!-- 文档输入区域 -->
            <section class="input-section">
                <h2 data-i18n="analysis.inputDocument">📝 输入文档</h2>

                <!-- 文件上传选项 -->
                <div class="upload-section">
                    <div class="upload-buttons">
                        <label for="file-upload" class="upload-btn">
                            <span data-i18n="analysis.uploadFile">📁 上传文件</span>
                            <input type="file" id="file-upload" accept=".txt,.md,.doc,.docx,.pdf" style="display: none;">
                        </label>
                        <label for="folder-upload" class="upload-btn">
                            <span data-i18n="analysis.uploadFolder">📂 上传文件夹</span>
                            <input type="file" id="folder-upload" webkitdirectory directory multiple style="display: none;">
                        </label>
                    </div>
                    <div id="file-list" class="file-list"></div>
                </div>

                <div class="divider" data-i18n="common.or">或</div>
                
                <!-- 手动输入 -->
                <div class="form-group">
                    <label for="doc-title" data-i18n="tableFields.title">文档标题</label>
                    <input type="text" id="doc-title" data-i18n="analysis.textPlaceholder" placeholder="请输入文档标题">
                </div>
                <div class="form-group">
                    <label for="doc-content" data-i18n="analysis.inputDocument">文档内容</label>
                    <textarea id="doc-content" rows="10" data-i18n="analysis.textPlaceholder" placeholder="请粘贴或输入文档内容..."></textarea>
                </div>
                <div class="form-group">
                    <label data-i18n="analysis.selectTasks">选择分析任务</label>
                    <div class="task-options">
                        <label class="task-option">
                            <input type="checkbox" name="task" value="actor_relation" checked>
                            <span data-i18n="analysis.actorRelation">行为者与关系分析</span>
                        </label>
                        <label class="task-option">
                            <input type="checkbox" name="task" value="role_framing">
                            <span data-i18n="analysis.roleFraming">角色塑造检测</span>
                        </label>
                        <label class="task-option">
                            <input type="checkbox" name="task" value="problem_scope">
                            <span data-i18n="analysis.problemScope">问题范围策略</span>
                        </label>
                        <label class="task-option">
                            <input type="checkbox" name="task" value="causal_mechanism">
                            <span data-i18n="analysis.causalMechanism">因果机制分析</span>
                        </label>
                    </div>
                </div>
                <button id="analyze-btn" class="primary-btn" data-i18n="analysis.startAnalysis">🚀 开始分析</button>
            </section>

            <!-- 结果显示区域 -->
            <section class="results-section" id="results-section" style="display: none;">
                <h2 data-i18n="analysis.viewResults">📊 分析结果</h2>
                <div id="loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    <p data-i18n="common.loading">正在分析文档，请稍候...</p>
                </div>
                <div id="results-container"></div>
            </section>

            <!-- 历史记录区域 -->
            <section class="history-section">
                <h2 data-i18n="analysis.quickActions">📚 历史记录</h2>
                <div id="history-list" class="history-list">
                    <p class="empty-state">暂无历史记录</p>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2024 文档分析系统 | Powered by 智谱AI</p>
        </footer>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>
