// 全局变量
let historyData = [];
let uploadedFiles = [];

// DOM元素
const analyzeBtn = document.getElementById('analyze-btn');
const docTitle = document.getElementById('doc-title');
const docContent = document.getElementById('doc-content');
const resultsSection = document.getElementById('results-section');
const resultsContainer = document.getElementById('results-container');
const loading = document.getElementById('loading');
const historyList = document.getElementById('history-list');
const fileUpload = document.getElementById('file-upload');
const folderUpload = document.getElementById('folder-upload');
const fileList = document.getElementById('file-list');
const apiKeyInput = document.getElementById('api-key');
const baseUrlInput = document.getElementById('base-url');

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    loadHistory();
    analyzeBtn.addEventListener('click', analyzeDocument);
    fileUpload.addEventListener('change', handleFileUpload);
    folderUpload.addEventListener('change', handleFolderUpload);
    
    // 加载保存的API密钥
    const savedApiKey = localStorage.getItem('zhipuApiKey');
    if (savedApiKey) {
        apiKeyInput.value = savedApiKey;
    }
    
    // 加载保存的Base URL
    const savedBaseUrl = localStorage.getItem('zhipuBaseUrl');
    if (savedBaseUrl) {
        baseUrlInput.value = savedBaseUrl;
    }
});

// 处理单文件上传
async function handleFileUpload(event) {
    const file = event.target.files[0];
    if (file) {
        await processFile(file);
    }
}

// 处理文件夹上传
async function handleFolderUpload(event) {
    const files = Array.from(event.target.files);
    uploadedFiles = [];
    fileList.innerHTML = '';
    
    for (const file of files) {
        // 处理支持的文件格式
        const supportedExtensions = ['.txt', '.md', '.doc', '.docx', '.pdf'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

        if (supportedExtensions.includes(fileExtension)) {
            await processFile(file, true);
        }
    }
    
    if (uploadedFiles.length > 0) {
        const message = `成功加载 ${uploadedFiles.length} 个文件，点击"开始分析"进行批量处理`;
        showMessage(message, 'success');

        // 显示批量处理提示
        const batchHint = document.createElement('div');
        batchHint.className = 'batch-hint';
        batchHint.innerHTML = `
            <p>📁 已加载 ${uploadedFiles.length} 个文件，准备进行批量分析</p>
            <p>💡 提示：点击下方的"开始分析"按钮将对所有文件进行批量处理</p>
        `;
        fileList.appendChild(batchHint);
    } else {
        showMessage('没有找到支持的文件格式（.txt, .md, .doc, .docx, .pdf）', 'warning');
    }
}

// 处理文件
async function processFile(file, isMultiple = false) {
    const reader = new FileReader();
    
    return new Promise((resolve) => {
        reader.onload = (e) => {
            const content = e.target.result;
            const fileName = file.name;
            const title = fileName.replace(/\.[^/.]+$/, ''); // 移除扩展名作为标题
            
            if (isMultiple) {
                // 多文件模式，添加到列表
                uploadedFiles.push({ title, content, fileName });
                addFileToList(fileName, uploadedFiles.length - 1);
            } else {
                // 单文件模式，直接填充到表单
                docTitle.value = title;
                docContent.value = content;
                showMessage(`文件 "${fileName}" 已加载`, 'success');
            }
            resolve();
        };
        
        reader.onerror = () => {
            showMessage(`无法读取文件: ${file.name}`, 'error');
            resolve();
        };
        
        reader.readAsText(file, 'UTF-8');
    });
}

// 添加文件到列表显示
function addFileToList(fileName, index) {
    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.innerHTML = `
        <span class="file-name">${fileName}</span>
        <span class="remove-btn" onclick="removeFile(${index})">×</span>
    `;
    fileItem.onclick = () => selectFile(index);
    fileList.appendChild(fileItem);
}

// 选择文件
function selectFile(index) {
    const file = uploadedFiles[index];
    if (file) {
        docTitle.value = file.title;
        docContent.value = file.content;
        showMessage(`已选择: ${file.fileName}`, 'success');
    }
}

// 移除文件
function removeFile(index) {
    uploadedFiles.splice(index, 1);
    updateFileList();
}

// 更新文件列表显示
function updateFileList() {
    fileList.innerHTML = '';
    uploadedFiles.forEach((file, index) => {
        addFileToList(file.fileName, index);
    });
}

// 分析文档
async function analyzeDocument() {
    // 保存API密钥（如果提供）
    const apiKey = apiKeyInput.value.trim();
    if (apiKey) {
        localStorage.setItem('zhipuApiKey', apiKey);
    }
    
    // 保存Base URL（如果提供）
    const baseUrl = baseUrlInput.value.trim();
    if (baseUrl) {
        localStorage.setItem('zhipuBaseUrl', baseUrl);
    }
    
    // 获取输入数据
    const title = docTitle.value.trim();
    const content = docContent.value.trim();

    // 检查是否有上传的文件
    if (uploadedFiles.length > 0) {
        // 如果有上传的文件，使用第一个文件或合并所有文件
        if (uploadedFiles.length === 1) {
            // 单个文件，使用文件的标题和内容
            const file = uploadedFiles[0];
            if (!title && file.title) {
                docTitle.value = file.title;
            }
            if (!content && file.content) {
                docContent.value = file.content;
            }
        } else {
            // 多个文件，使用批量处理
            await processBatchAnalysis();
            return;
        }
    }

    // 重新获取可能更新的数据
    const finalTitle = docTitle.value.trim();
    const finalContent = docContent.value.trim();

    // 验证输入
    if (!finalTitle) {
        showMessage(t('errors.noDocumentTitle'), 'error');
        return;
    }

    if (!finalContent) {
        showMessage(t('errors.noDocumentContent'), 'error');
        return;
    }
    
    // 获取选中的任务
    const tasks = [];
    document.querySelectorAll('input[name="task"]:checked').forEach(checkbox => {
        tasks.push(checkbox.value);
    });
    
    if (tasks.length === 0) {
        showMessage(t('errors.noTaskSelected'), 'error');
        return;
    }
    
    // 显示加载状态
    resultsSection.style.display = 'block';
    loading.style.display = 'block';
    resultsContainer.innerHTML = '';
    
    try {
        // 生成文档ID
        const docId = `doc_${Date.now()}`;
        
        // 构建请求头
        const headers = {
            'Content-Type': 'application/json'
        };
        
        // 如果提供了API密钥，添加到请求头
        if (apiKey) {
            headers['X-API-Key'] = apiKey;
        }
        
        // 创建文档
        const docResponse = await fetch('/api/v1/documents/', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({
                doc_id: docId,
                title: title,
                text: content,
                metadata: {
                    source: 'Web UI',
                    created_at: new Date().toISOString(),
                    api_key: apiKey,  // 传递API密钥到后端
                    base_url: baseUrl  // 传递Base URL到后端
                }
            })
        });
        
        if (!docResponse.ok) {
            throw new Error('创建文档失败');
        }
        
        // 执行分析
        const analysisResponse = await fetch('/api/v1/analysis/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                doc_id: docId,
                tasks: tasks
            })
        });
        
        if (!analysisResponse.ok) {
            throw new Error('分析文档失败');
        }
        
        const results = await analysisResponse.json();
        
        // 隐藏加载状态
        loading.style.display = 'none';
        
        // 显示结果
        displayResults(results);
        
        // 添加到历史记录
        addToHistory(docId, title, tasks);
        
        // 保存当前文档ID到sessionStorage，供可视化页面使用
        sessionStorage.setItem('currentDocId', docId);
        sessionStorage.setItem('fromAnalysis', 'true');
        
        showMessage('分析完成！', 'success');
        
    } catch (error) {
        loading.style.display = 'none';
        showMessage(`错误: ${error.message}`, 'error');
        console.error('分析错误:', error);
    }
}

// 批量分析处理
async function processBatchAnalysis() {
    try {
        console.log('开始批量分析，文件数量:', uploadedFiles.length);

        // 检查是否有文件
        if (uploadedFiles.length === 0) {
            showMessage('请先上传文件', 'error');
            return;
        }

        // 显示加载状态
        loading.style.display = 'block';
        analyzeBtn.disabled = true;
        analyzeBtn.textContent = t('common.loading');

        // 获取选中的任务
        const taskCheckboxes = document.querySelectorAll('input[name="task"]:checked');
        const tasks = Array.from(taskCheckboxes).map(cb => cb.value);

        console.log('选中的任务:', tasks);

        // 检查是否选择了任务
        if (tasks.length === 0) {
            showMessage('请至少选择一个分析任务', 'error');
            loading.style.display = 'none';
            analyzeBtn.disabled = false;
            analyzeBtn.textContent = t('analysis.startAnalysis');
            return;
        }

        if (tasks.length === 0) {
            showMessage(t('errors.noTaskSelected'), 'error');
            return;
        }

        // 首先上传所有文件并获取文档ID
        const documentIds = [];

        for (let i = 0; i < uploadedFiles.length; i++) {
            const file = uploadedFiles[i];
            console.log(`上传文档 ${i + 1}/${uploadedFiles.length}: ${file.fileName}`);

            // 创建文档数据
            const docData = {
                title: file.title,
                text: file.content,  // 使用 text 字段而不是 content
                metadata: {
                    original_filename: file.fileName,
                    upload_method: "batch_upload"
                }
            };

            // 添加API密钥到metadata（如果有）
            const apiKey = apiKeyInput.value.trim();
            if (apiKey) {
                docData.metadata.api_key = apiKey;
            }

            // 添加Base URL到metadata（如果有）
            const baseUrl = baseUrlInput.value.trim();
            if (baseUrl) {
                docData.metadata.base_url = baseUrl;
            }

            const docResponse = await fetch('/api/v1/documents/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(docData)
            });

            if (!docResponse.ok) {
                const errorText = await docResponse.text();
                console.error(`上传文档失败:`, errorText);
                throw new Error(`上传文档 "${file.fileName}" 失败: ${errorText}`);
            }

            const docResult = await docResponse.json();
            const docId = docResult.doc_id || docResult.id;
            documentIds.push(docId);
            console.log(`文档上传成功，ID: ${docId}`);
        }

        console.log('所有文档上传完成，开始批量分析...');
        console.log('文档IDs:', documentIds);

        // 启动批量分析
        const batchResponse = await fetch('/api/v1/batch/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                document_ids: documentIds,
                tasks: tasks
            })
        });

        if (!batchResponse.ok) {
            const errorText = await batchResponse.text();
            console.error('批量分析启动失败:', errorText);
            throw new Error(`批量分析启动失败: ${errorText}`);
        }

        const batchData = await batchResponse.json();
        console.log('批量分析已启动，作业ID:', batchData.job_id);

        // 显示批量分析状态
        showBatchProgress(batchData.job_id, documentIds.length);

        showMessage(`批量分析已启动，正在处理 ${documentIds.length} 个文件`, 'success');

    } catch (error) {
        console.error('批量分析失败:', error);
        showMessage(`批量分析失败: ${error.message}`, 'error');

        // 隐藏加载状态
        loading.style.display = 'none';
        analyzeBtn.disabled = false;
        analyzeBtn.textContent = t('analysis.startAnalysis');
    }
}

// 显示批量分析进度
function showBatchProgress(jobId, fileCount) {
    resultsContainer.innerHTML = '';
    resultsContainer.style.display = 'block';

    const progressContainer = document.createElement('div');
    progressContainer.className = 'batch-progress-container';
    progressContainer.innerHTML = `
        <h3>📊 批量分析进行中</h3>
        <p>正在分析 ${fileCount} 个文件...</p>
        <div class="progress-bar">
            <div class="progress-fill" id="batch-progress-fill" style="width: 0%"></div>
        </div>
        <div id="batch-status">准备中...</div>
        <button onclick="checkBatchStatus('${jobId}')" class="btn btn-primary">检查进度</button>
    `;
    resultsContainer.appendChild(progressContainer);

    // 开始轮询状态
    pollBatchStatus(jobId);
}

// 轮询批量分析状态
async function pollBatchStatus(jobId) {
    const maxAttempts = 60; // 最多轮询5分钟
    let attempts = 0;

    const poll = async () => {
        try {
            const response = await fetch(`/api/v1/batch/status/${jobId}`);
            if (!response.ok) {
                throw new Error('无法获取批量分析状态');
            }

            const status = await response.json();
            updateBatchProgress(status);

            if (status.status === 'completed' || status.status === 'failed') {
                if (status.status === 'completed') {
                    showBatchResults(jobId);
                } else {
                    showMessage('批量分析失败', 'error');
                }
                return;
            }

            attempts++;
            if (attempts < maxAttempts) {
                setTimeout(poll, 5000); // 每5秒轮询一次
            } else {
                showMessage('分析超时，请手动检查状态', 'warning');
            }
        } catch (error) {
            console.error('轮询状态失败:', error);
            showMessage('无法获取分析状态', 'error');
        }
    };

    poll();
}

// 更新批量分析进度
function updateBatchProgress(status) {
    const progressFill = document.getElementById('batch-progress-fill');
    const statusDiv = document.getElementById('batch-status');

    if (progressFill && statusDiv) {
        const progress = (status.completed / status.total) * 100;
        progressFill.style.width = `${progress}%`;
        statusDiv.textContent = `已完成 ${status.completed}/${status.total} 个文件`;
    }
}

// 显示批量分析结果
async function showBatchResults(jobId) {
    try {
        const response = await fetch(`/api/v1/batch/results/${jobId}`);
        if (!response.ok) {
            throw new Error('无法获取批量分析结果');
        }

        const results = await response.json();
        displayBatchResults(results);
    } catch (error) {
        console.error('获取批量结果失败:', error);
        showMessage('无法获取分析结果', 'error');
    }
}

// 显示批量分析结果
function displayBatchResults(batchData) {
    resultsContainer.innerHTML = '';

    const batchResultsContainer = document.createElement('div');
    batchResultsContainer.className = 'batch-results-container';
    batchResultsContainer.innerHTML = `
        <h3>📊 批量分析结果</h3>
        <p>作业ID: ${batchData.job_id}</p>
        <p>状态: ${batchData.status}</p>
        <p>共分析了 ${batchData.results.length} 个文件 (${batchData.completed}/${batchData.total})</p>
        <button onclick="exportBatchResults('${batchData.job_id}')" class="btn btn-secondary">导出所有结果</button>
    `;
    resultsContainer.appendChild(batchResultsContainer);

    // 显示每个文件的结果
    batchData.results.forEach((result, index) => {
        const resultCard = document.createElement('div');
        resultCard.className = 'batch-result-card';

        let resultContent = '';
        if (result.status === 'completed' && result.results) {
            // 显示分析任务结果
            for (const [taskType, taskResult] of Object.entries(result.results)) {
                resultContent += `
                    <div class="task-result">
                        <h5>${getTaskName(taskType)}</h5>
                        <div class="task-content">
                            ${taskResult.status === 'completed' ?
                                `<pre>${formatResult(taskResult.result)}</pre>` :
                                `<p class="error">错误: ${taskResult.error}</p>`
                            }
                        </div>
                    </div>
                `;
            }
        }

        resultCard.innerHTML = `
            <h4>文件 ${index + 1}: ${result.document_title}</h4>
            <div class="result-summary">
                <p>状态: ${result.status === 'completed' ? '✅ 完成' : '❌ 失败'}</p>
                ${result.status === 'completed' ?
                    `<button onclick="toggleResultDetails('result-${index}')" class="btn btn-info">查看详细结果</button>` :
                    `<p class="error">错误: ${result.error}</p>`
                }
            </div>
            <div id="result-${index}" class="detailed-results" style="display: none;">
                ${resultContent}
            </div>
        `;
        resultsContainer.appendChild(resultCard);
    });
}

// 切换详细结果显示
function toggleResultDetails(resultId) {
    const detailsDiv = document.getElementById(resultId);
    if (detailsDiv) {
        detailsDiv.style.display = detailsDiv.style.display === 'none' ? 'block' : 'none';
    }
}

// 导出批量结果
function exportBatchResults(jobId) {
    // 这里可以实现导出功能，比如生成CSV或JSON文件
    console.log('导出批量结果:', jobId);
    showMessage('导出功能开发中...', 'info');
}

// 显示分析结果
function displayResults(results) {
    resultsContainer.innerHTML = '';

    // 添加可视化按钮
    const visualizationBtn = document.createElement('div');
    visualizationBtn.className = 'visualization-btn-container';
    visualizationBtn.innerHTML = `
        <button class="viz-btn" onclick="switchToVisualization()">
            📊 查看数据可视化
        </button>
    `;
    resultsContainer.appendChild(visualizationBtn);
    
    for (const [taskType, result] of Object.entries(results)) {
        const resultCard = document.createElement('div');
        resultCard.className = 'result-card';
        
        const taskName = getTaskName(taskType);
        
        resultCard.innerHTML = `
            <h3>${taskName}</h3>
            <div class="result-content">
                <pre>${formatResult(result)}</pre>
            </div>
        `;
        
        resultsContainer.appendChild(resultCard);
    }
}

// 格式化结果
function formatResult(result) {
    if (result.success === false) {
        return `分析失败: ${result.error || '未知错误'}`;
    }
    
    if (result.result) {
        if (result.result.raw_analysis) {
            return result.result.raw_analysis;
        }
        return JSON.stringify(result.result, null, 2);
    }
    
    return JSON.stringify(result, null, 2);
}

// 获取任务名称
function getTaskName(taskType) {
    const taskNames = {
        'actor_relation': '行为者与关系分析',
        'role_framing': '角色塑造检测',
        'problem_scope': '问题范围策略',
        'causal_mechanism': '因果机制分析'
    };
    return taskNames[taskType] || taskType;
}

// 添加到历史记录
function addToHistory(docId, title, tasks) {
    const historyItem = {
        id: docId,
        title: title,
        tasks: tasks,
        date: new Date().toLocaleString('zh-CN')
    };
    
    historyData.unshift(historyItem);
    
    // 只保留最近10条记录
    if (historyData.length > 10) {
        historyData = historyData.slice(0, 10);
    }
    
    // 保存到本地存储
    localStorage.setItem('analysisHistory', JSON.stringify(historyData));
    
    // 更新显示
    updateHistoryDisplay();
}

// 加载历史记录
function loadHistory() {
    const stored = localStorage.getItem('analysisHistory');
    if (stored) {
        try {
            historyData = JSON.parse(stored);
            updateHistoryDisplay();
        } catch (e) {
            console.error('加载历史记录失败:', e);
        }
    }
}

// 更新历史记录显示
function updateHistoryDisplay() {
    if (historyData.length === 0) {
        historyList.innerHTML = '<p class="empty-state">暂无历史记录</p>';
        return;
    }
    
    historyList.innerHTML = '';
    
    historyData.forEach(item => {
        const historyItem = document.createElement('div');
        historyItem.className = 'history-item';
        historyItem.innerHTML = `
            <h4>${item.title}</h4>
            <div class="date">${item.date}</div>
        `;
        
        historyItem.addEventListener('click', () => {
            loadHistoryItem(item.id);
        });
        
        historyList.appendChild(historyItem);
    });
}

// 加载历史记录项
async function loadHistoryItem(docId) {
    try {
        const response = await fetch(`/api/v1/analysis/results/${docId}`);
        if (response.ok) {
            const results = await response.json();
            resultsSection.style.display = 'block';
            displayResults(results.results);
            showMessage('已加载历史记录', 'success');
        } else {
            showMessage('无法加载历史记录', 'error');
        }
    } catch (error) {
        showMessage(`加载失败: ${error.message}`, 'error');
    }
}

// 切换到数据可视化页面
function switchToVisualization() {
    // 获取当前分析的文档ID
    const currentDocId = historyData.length > 0 ? historyData[0].id : null;
    
    if (currentDocId) {
        // 保存当前文档ID到sessionStorage，供可视化页面使用
        sessionStorage.setItem('currentDocId', currentDocId);
        sessionStorage.setItem('fromAnalysis', 'true');
    }
    
    // 跳转到可视化页面
    window.location.href = '/static/visualization.html';
}

// 显示消息
function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
    messageDiv.textContent = message;
    
    // 插入到主内容区域顶部
    const main = document.querySelector('main');
    main.insertBefore(messageDiv, main.firstChild);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}
